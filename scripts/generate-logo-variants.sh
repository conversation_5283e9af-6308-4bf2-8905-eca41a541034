#!/bin/bash

# Logo Variant Generator Script
# Generates multiple logo variants from the source logo.png file
# Requirements: ImageMagick, Inkscape (for SVG conversion)

set -e  # Exit on any error

# Configuration
SOURCE_LOGO="public/logo.png"
OUTPUT_DIR="public/logos"
TEMP_DIR="temp_logo_processing"

# Colors
WHITE="#FFFFFF"
BLACK="#000000"
TRANSPARENT="none"

# Create directories
mkdir -p "$OUTPUT_DIR"
mkdir -p "$TEMP_DIR"

echo "🎨 Starting logo variant generation..."
echo "Source: $SOURCE_LOGO"
echo "Output directory: $OUTPUT_DIR"

# Check if source file exists
if [ ! -f "$SOURCE_LOGO" ]; then
    echo "❌ Error: Source logo file not found at $SOURCE_LOGO"
    exit 1
fi

# Check dependencies
command -v convert >/dev/null 2>&1 || { echo "❌ ImageMagick (convert) is required but not installed. Install with: brew install imagemagick"; exit 1; }
command -v magick >/dev/null 2>&1 || { echo "⚠️  Warning: 'magick' command not found, using 'convert' instead"; }

echo "✅ Dependencies check passed"

# Function to create PNG variants
create_png_variants() {
    echo "📸 Creating PNG variants..."
    
    # 1. Light logo without background (remove black background, keep white logo)
    echo "  → Light logo without background"
    convert "$SOURCE_LOGO" \
        -fuzz 20% -transparent black \
        "$OUTPUT_DIR/logo-light-no-bg.png"
    
    # 2. Dark logo on white background (invert colors)
    echo "  → Dark logo on white background"
    convert "$SOURCE_LOGO" \
        -negate \
        "$OUTPUT_DIR/logo-dark-white-bg.png"
    
    # 3. Dark logo without background (invert and remove background)
    echo "  → Dark logo without background"
    convert "$SOURCE_LOGO" \
        -negate \
        -fuzz 20% -transparent white \
        "$OUTPUT_DIR/logo-dark-no-bg.png"
    
    # Also create the original with a descriptive name
    echo "  → Original logo (light on black background)"
    cp "$SOURCE_LOGO" "$OUTPUT_DIR/logo-light-black-bg.png"
}

# Function to convert PNG to SVG
png_to_svg() {
    local input_png="$1"
    local output_svg="$2"
    local bg_color="$3"
    
    echo "  → Converting $(basename "$input_png") to SVG"
    
    # Get image dimensions
    dimensions=$(identify -format "%wx%h" "$input_png")
    width=$(echo $dimensions | cut -d'x' -f1)
    height=$(echo $dimensions | cut -d'x' -f2)
    
    # Convert PNG to base64 for embedding
    base64_data=$(base64 -i "$input_png")
    
    # Create SVG with embedded PNG
    cat > "$output_svg" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<svg width="$width" height="$height" viewBox="0 0 $width $height" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .logo-image { width: 100%; height: 100%; }
    </style>
  </defs>
EOF

    if [ "$bg_color" != "none" ]; then
        echo "  <rect width=\"100%\" height=\"100%\" fill=\"$bg_color\"/>" >> "$output_svg"
    fi
    
    echo "  <image class=\"logo-image\" href=\"data:image/png;base64,$base64_data\"/>" >> "$output_svg"
    echo "</svg>" >> "$output_svg"
}

# Function to create SVG variants
create_svg_variants() {
    echo "🎯 Creating SVG variants..."
    
    # Convert each PNG variant to SVG
    png_to_svg "$OUTPUT_DIR/logo-light-black-bg.png" "$OUTPUT_DIR/logo-light-black-bg.svg" "$BLACK"
    png_to_svg "$OUTPUT_DIR/logo-light-no-bg.png" "$OUTPUT_DIR/logo-light-no-bg.svg" "none"
    png_to_svg "$OUTPUT_DIR/logo-dark-white-bg.png" "$OUTPUT_DIR/logo-dark-white-bg.svg" "$WHITE"
    png_to_svg "$OUTPUT_DIR/logo-dark-no-bg.png" "$OUTPUT_DIR/logo-dark-no-bg.svg" "none"
}

# Function to optimize images
optimize_images() {
    echo "⚡ Optimizing images..."
    
    # Optimize PNG files
    for png_file in "$OUTPUT_DIR"/*.png; do
        if [ -f "$png_file" ]; then
            echo "  → Optimizing $(basename "$png_file")"
            convert "$png_file" -strip -quality 95 "$png_file"
        fi
    done
}

# Function to create different sizes
create_sizes() {
    echo "📏 Creating different sizes..."
    
    local sizes=(16 32 48 64 128 256 512)
    
    for size in "${sizes[@]}"; do
        mkdir -p "$OUTPUT_DIR/sizes/${size}x${size}"
        
        for variant in logo-light-black-bg logo-light-no-bg logo-dark-white-bg logo-dark-no-bg; do
            if [ -f "$OUTPUT_DIR/${variant}.png" ]; then
                echo "  → Creating ${size}x${size} for $variant"
                convert "$OUTPUT_DIR/${variant}.png" \
                    -resize "${size}x${size}" \
                    -quality 95 \
                    "$OUTPUT_DIR/sizes/${size}x${size}/${variant}.png"
            fi
        done
    done
}

# Function to generate summary
generate_summary() {
    echo ""
    echo "📋 Generation Summary:"
    echo "===================="
    
    echo ""
    echo "Main variants created:"
    ls -la "$OUTPUT_DIR"/*.png "$OUTPUT_DIR"/*.svg 2>/dev/null | while read line; do
        echo "  $line"
    done
    
    echo ""
    echo "Size variants created:"
    find "$OUTPUT_DIR/sizes" -name "*.png" 2>/dev/null | wc -l | xargs echo "  Total size variants:"
    
    echo ""
    echo "Usage recommendations:"
    echo "  • logo-light-black-bg: Original style, use on light backgrounds"
    echo "  • logo-light-no-bg: Light logo for dark backgrounds/overlays"
    echo "  • logo-dark-white-bg: Dark logo for light themes"
    echo "  • logo-dark-no-bg: Dark logo for transparent backgrounds"
    echo ""
    echo "  • SVG files: Best for web use, scalable"
    echo "  • PNG files: Best for specific sizes, better browser support"
    echo "  • Size variants: Use appropriate size for performance"
}

# Main execution
main() {
    echo "🚀 Starting logo variant generation process..."
    
    create_png_variants
    create_svg_variants
    optimize_images
    create_sizes
    
    # Cleanup
    rm -rf "$TEMP_DIR"
    
    generate_summary
    
    echo ""
    echo "✅ Logo variant generation completed successfully!"
    echo "📁 All variants saved to: $OUTPUT_DIR"
}

# Run main function
main "$@"
