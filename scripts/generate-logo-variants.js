#!/usr/bin/env node

/**
 * Logo Variant Generator Script (Node.js version)
 * Generates multiple logo variants from the source logo.png file
 * Requirements: ImageMagick (convert command)
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const SOURCE_LOGO = 'public/logo.png';
const OUTPUT_DIR = 'public/logos';
const TEMP_DIR = 'temp_logo_processing';

// Colors
const WHITE = '#FFFFFF';
const BLACK = '#000000';

class LogoGenerator {
    constructor() {
        this.sourceExists = false;
        this.hasImageMagick = false;
    }

    // Check if required dependencies are available
    checkDependencies() {
        console.log('🔍 Checking dependencies...');
        
        // Check if source file exists
        if (!fs.existsSync(SOURCE_LOGO)) {
            console.error(`❌ Error: Source logo file not found at ${SOURCE_LOGO}`);
            process.exit(1);
        }
        this.sourceExists = true;
        console.log('✅ Source logo file found');

        // Check ImageMagick
        try {
            execSync('convert -version', { stdio: 'ignore' });
            this.hasImageMagick = true;
            console.log('✅ ImageMagick found');
        } catch (error) {
            console.error('❌ ImageMagick (convert) is required but not installed.');
            console.error('Install with: brew install imagemagick (macOS) or apt-get install imagemagick (Ubuntu)');
            process.exit(1);
        }
    }

    // Create output directories
    createDirectories() {
        console.log('📁 Creating directories...');
        
        if (!fs.existsSync(OUTPUT_DIR)) {
            fs.mkdirSync(OUTPUT_DIR, { recursive: true });
        }
        
        if (!fs.existsSync(TEMP_DIR)) {
            fs.mkdirSync(TEMP_DIR, { recursive: true });
        }
    }

    // Execute ImageMagick command safely
    executeCommand(command, description) {
        try {
            console.log(`  → ${description}`);
            execSync(command, { stdio: 'pipe' });
        } catch (error) {
            console.error(`❌ Failed: ${description}`);
            console.error(`Command: ${command}`);
            console.error(`Error: ${error.message}`);
        }
    }

    // Create PNG variants
    createPngVariants() {
        console.log('📸 Creating PNG variants...');

        // 1. Light logo without background (remove black background, keep white logo)
        this.executeCommand(
            `convert "${SOURCE_LOGO}" -fuzz 20% -transparent black "${OUTPUT_DIR}/logo-light-no-bg.png"`,
            'Light logo without background'
        );

        // 2. Dark logo on white background (invert colors)
        this.executeCommand(
            `convert "${SOURCE_LOGO}" -negate "${OUTPUT_DIR}/logo-dark-white-bg.png"`,
            'Dark logo on white background'
        );

        // 3. Dark logo without background (invert and remove background)
        this.executeCommand(
            `convert "${SOURCE_LOGO}" -negate -fuzz 20% -transparent white "${OUTPUT_DIR}/logo-dark-no-bg.png"`,
            'Dark logo without background'
        );

        // Also create the original with a descriptive name
        console.log('  → Original logo (light on black background)');
        fs.copyFileSync(SOURCE_LOGO, `${OUTPUT_DIR}/logo-light-black-bg.png`);
    }

    // Get image dimensions
    getImageDimensions(imagePath) {
        try {
            const result = execSync(`identify -format "%wx%h" "${imagePath}"`, { encoding: 'utf8' });
            const [width, height] = result.trim().split('x');
            return { width: parseInt(width), height: parseInt(height) };
        } catch (error) {
            console.error(`Failed to get dimensions for ${imagePath}`);
            return { width: 200, height: 200 }; // fallback
        }
    }

    // Convert PNG to base64
    pngToBase64(imagePath) {
        try {
            const imageBuffer = fs.readFileSync(imagePath);
            return imageBuffer.toString('base64');
        } catch (error) {
            console.error(`Failed to convert ${imagePath} to base64`);
            return '';
        }
    }

    // Create SVG from PNG
    createSvgFromPng(inputPng, outputSvg, backgroundColor = 'none') {
        console.log(`  → Converting ${path.basename(inputPng)} to SVG`);
        
        const dimensions = this.getImageDimensions(inputPng);
        const base64Data = this.pngToBase64(inputPng);
        
        if (!base64Data) return;

        const svgContent = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${dimensions.width}" height="${dimensions.height}" viewBox="0 0 ${dimensions.width} ${dimensions.height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .logo-image { width: 100%; height: 100%; }
    </style>
  </defs>
  ${backgroundColor !== 'none' ? `<rect width="100%" height="100%" fill="${backgroundColor}"/>` : ''}
  <image class="logo-image" href="data:image/png;base64,${base64Data}"/>
</svg>`;

        try {
            fs.writeFileSync(outputSvg, svgContent);
        } catch (error) {
            console.error(`Failed to write SVG file: ${outputSvg}`);
        }
    }

    // Create SVG variants
    createSvgVariants() {
        console.log('🎯 Creating SVG variants...');

        const variants = [
            { png: 'logo-light-black-bg.png', svg: 'logo-light-black-bg.svg', bg: BLACK },
            { png: 'logo-light-no-bg.png', svg: 'logo-light-no-bg.svg', bg: 'none' },
            { png: 'logo-dark-white-bg.png', svg: 'logo-dark-white-bg.svg', bg: WHITE },
            { png: 'logo-dark-no-bg.png', svg: 'logo-dark-no-bg.svg', bg: 'none' }
        ];

        variants.forEach(variant => {
            const pngPath = path.join(OUTPUT_DIR, variant.png);
            const svgPath = path.join(OUTPUT_DIR, variant.svg);
            
            if (fs.existsSync(pngPath)) {
                this.createSvgFromPng(pngPath, svgPath, variant.bg);
            }
        });
    }

    // Optimize PNG images
    optimizeImages() {
        console.log('⚡ Optimizing images...');
        
        const pngFiles = fs.readdirSync(OUTPUT_DIR).filter(file => file.endsWith('.png'));
        
        pngFiles.forEach(file => {
            const filePath = path.join(OUTPUT_DIR, file);
            this.executeCommand(
                `convert "${filePath}" -strip -quality 95 "${filePath}"`,
                `Optimizing ${file}`
            );
        });
    }

    // Create different sizes
    createSizes() {
        console.log('📏 Creating different sizes...');
        
        const sizes = [16, 32, 48, 64, 128, 256, 512];
        const variants = ['logo-light-black-bg', 'logo-light-no-bg', 'logo-dark-white-bg', 'logo-dark-no-bg'];
        
        sizes.forEach(size => {
            const sizeDir = path.join(OUTPUT_DIR, 'sizes', `${size}x${size}`);
            if (!fs.existsSync(sizeDir)) {
                fs.mkdirSync(sizeDir, { recursive: true });
            }
            
            variants.forEach(variant => {
                const sourcePath = path.join(OUTPUT_DIR, `${variant}.png`);
                const targetPath = path.join(sizeDir, `${variant}.png`);
                
                if (fs.existsSync(sourcePath)) {
                    this.executeCommand(
                        `convert "${sourcePath}" -resize ${size}x${size} -quality 95 "${targetPath}"`,
                        `Creating ${size}x${size} for ${variant}`
                    );
                }
            });
        });
    }

    // Generate summary
    generateSummary() {
        console.log('\n📋 Generation Summary:');
        console.log('====================');
        
        console.log('\nMain variants created:');
        try {
            const files = fs.readdirSync(OUTPUT_DIR);
            files.filter(file => file.endsWith('.png') || file.endsWith('.svg'))
                 .forEach(file => {
                     const stats = fs.statSync(path.join(OUTPUT_DIR, file));
                     console.log(`  ${file} (${Math.round(stats.size / 1024)}KB)`);
                 });
        } catch (error) {
            console.log('  Could not list files');
        }

        console.log('\nUsage recommendations:');
        console.log('  • logo-light-black-bg: Original style, use on light backgrounds');
        console.log('  • logo-light-no-bg: Light logo for dark backgrounds/overlays');
        console.log('  • logo-dark-white-bg: Dark logo for light themes');
        console.log('  • logo-dark-no-bg: Dark logo for transparent backgrounds');
        console.log('\n  • SVG files: Best for web use, scalable');
        console.log('  • PNG files: Best for specific sizes, better browser support');
        console.log('  • Size variants: Use appropriate size for performance');
    }

    // Cleanup temporary files
    cleanup() {
        if (fs.existsSync(TEMP_DIR)) {
            fs.rmSync(TEMP_DIR, { recursive: true, force: true });
        }
    }

    // Main execution
    run() {
        console.log('🎨 Starting logo variant generation...');
        console.log(`Source: ${SOURCE_LOGO}`);
        console.log(`Output directory: ${OUTPUT_DIR}`);
        
        try {
            this.checkDependencies();
            this.createDirectories();
            this.createPngVariants();
            this.createSvgVariants();
            this.optimizeImages();
            this.createSizes();
            this.generateSummary();
            
            console.log('\n✅ Logo variant generation completed successfully!');
            console.log(`📁 All variants saved to: ${OUTPUT_DIR}`);
        } catch (error) {
            console.error('\n❌ Logo generation failed:', error.message);
            process.exit(1);
        } finally {
            this.cleanup();
        }
    }
}

// Run the generator
if (require.main === module) {
    const generator = new LogoGenerator();
    generator.run();
}

module.exports = LogoGenerator;
